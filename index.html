<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
        }

        .logo {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 107, 53, 0.3);
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            margin: 0 40px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.15);
            border-color: #ff6b35;
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-btn {
            position: relative;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-btn:hover {
            background: rgba(255, 107, 53, 0.2);
            transform: scale(1.1);
        }

        .profile-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .profile-img:hover {
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr 320px;
            gap: 30px;
            padding: 30px 0;
            min-height: calc(100vh - 80px);
        }

        /* Sidebar */
        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 110px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar h3 {
            color: #ff6b35;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 600;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            margin-bottom: 8px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(45deg, rgba(255, 107, 53, 0.2), rgba(247, 147, 30, 0.2));
            border-color: #ff6b35;
        }

        .nav-icon {
            font-size: 20px;
            color: #ff6b35;
        }

        /* Feed Section */
        .feed {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .feed-header {
            padding: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feed-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .story-prompt {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 107, 53, 0.3);
            margin-bottom: 20px;
        }

        .story-prompt h4 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .story-prompt p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .create-post-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .create-post-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }

        .create-post-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .create-post-btn:hover::before {
            left: 100%;
        }

        .feed-content {
            padding: 30px;
        }

        .load-more {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .load-more:hover {
            color: #ff6b35;
        }

        /* Trending Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 110px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .trending h3 {
            color: #ff6b35;
            margin-bottom: 25px;
            font-size: 20px;
            font-weight: 600;
        }

        .trending-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .trending-item:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-2px);
        }

        .trending-hashtag {
            color: #ff6b35;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .trending-count {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .sidebar, .feed, .trending {
            animation: fadeIn 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 280px 1fr 300px;
                gap: 20px;
            }
        }

        @media (max-width: 960px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .sidebar, .trending {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .search-container {
                max-width: 100%;
                margin: 0;
            }

            .container {
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">Naroop</div>
                <div class="search-container">
                    <div class="search-icon">🔍</div>
                    <input type="text" class="search-input" placeholder="Search communities, topics, people...">
                </div>
                <div class="user-actions">
                    <button class="notification-btn">🔔</button>
                    <div class="profile-img">N</div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content">
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="nav-item active" data-section="feed">
                    <div class="nav-icon">📱</div>
                    <span>Feed</span>
                </div>
                <div class="nav-item" data-section="explore">
                    <div class="nav-icon">🌟</div>
                    <span>Explore</span>
                </div>
                <div class="nav-item" data-section="messages">
                    <div class="nav-icon">💬</div>
                    <span>Messages</span>
                </div>
                <div class="nav-item" data-section="profile">
                    <div class="nav-icon">👤</div>
                    <span>Profile</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>Communities</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📊</div>
                    <span>Analytics</span>
                </div>
            </aside>

            <main class="feed">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <div class="story-prompt">
                        <h4>✨ Share Your Story</h4>
                        <p>What positive experience would you like to share with the community today?</p>
                        <button class="create-post-btn" id="createPostBtn">Create Post</button>
                    </div>
                </div>
                <div class="feed-content">
                    <div id="postsContainer">
                        <!-- Posts will be loaded here -->
                    </div>
                    <div class="load-more">Load More Posts</div>
                </div>
            </main>

            <aside class="trending">
                <h3>Trending Topics</h3>
                <div class="trending-item">
                    <div class="trending-hashtag">#BlackExcellence</div>
                    <div class="trending-count">2.1M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#CommunityLove</div>
                    <div class="trending-count">980K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Inspiration</div>
                    <div class="trending-count">1.5M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#BlackJoy</div>
                    <div class="trending-count">750K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Success</div>
                    <div class="trending-count">1.2M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Empowerment</div>
                    <div class="trending-count">890K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-hashtag">#Culture</div>
                    <div class="trending-count">650K posts</div>
                </div>
            </aside>
        </div>
    </div>

    <script>
        // Add interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation item interactions
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Search functionality
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });
            searchInput.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });

            // Trending item interactions
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const hashtag = this.querySelector('.trending-hashtag').textContent;
                    searchInput.value = hashtag;
                    searchInput.focus();
                });
            });

            // Create post button animation
            const createPostBtn = document.querySelector('.create-post-btn');
            createPostBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });

            // Profile image interaction
            const profileImg = document.querySelector('.profile-img');
            profileImg.addEventListener('click', function() {
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>

    <!-- Mobile Responsive Styles -->
    <style>
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding-bottom: 80px;
            }

            .sidebar,
            .trending {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .feed-section {
                padding: 20px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
            }

            .create-post-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .create-post p {
                font-size: 13px;
            }

            .create-btn {
                padding: 12px 24px;
                font-size: 14px;
            }

            .post-card {
                padding: 20px;
                margin-bottom: 16px;
            }

            .post-title {
                font-size: 16px;
            }

            .post-text {
                font-size: 14px;
            }

            .action-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .header {
                padding: 12px 15px;
                border-radius: 15px;
            }

            .logo {
                font-size: 20px;
            }

            .nav-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .feed-section {
                padding: 15px;
                border-radius: 15px;
            }

            .create-post {
                padding: 16px;
                border-radius: 12px;
            }

            .create-post-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .create-post h3 {
                font-size: 15px;
            }

            .post-card {
                padding: 16px;
                border-radius: 12px;
            }

            .author-avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .post-title {
                font-size: 15px;
            }

            .action-btn {
                padding: 6px 8px;
                font-size: 12px;
            }

            .mobile-nav-item .icon {
                font-size: 18px;
            }

            .mobile-nav-item .label {
                font-size: 10px;
            }
        }

        /* Create Post Modal Styles */
        .post-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .post-modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .post-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .post-modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #666;
        }

        .post-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 18px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c9a876;
            background: white;
            box-shadow: 0 0 0 3px rgba(201, 168, 118, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .cancel-btn,
        .submit-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            min-width: 100px;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e1e5e9;
        }

        .cancel-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .submit-btn {
            background: linear-gradient(45deg, #c9a876, #d4b896);
            color: white;
            box-shadow: 0 4px 15px rgba(201, 168, 118, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .post-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            color: #666;
            font-weight: 500;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #c9a876;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile responsive styles for modal */
        @media (max-width: 768px) {
            .post-modal {
                padding: 10px;
            }

            .post-modal-content {
                border-radius: 15px;
                max-height: 95vh;
            }

            .post-modal-header {
                padding: 20px 20px 15px;
            }

            .post-modal-header h3 {
                font-size: 20px;
            }

            .post-form {
                padding: 20px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-actions {
                flex-direction: column-reverse;
                gap: 10px;
            }

            .cancel-btn,
            .submit-btn {
                width: 100%;
                padding: 15px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script>
        // Authentication functions (placeholders)
        function showSignIn() {
            // Always clear any stored user/auth data and sign out from Firebase before showing sign-in UI
            console.log('🔐 Sign In button clicked - clearing auth state first...');

            if (window.FirebaseAuth && typeof window.FirebaseAuth.signOut === 'function') {
                window.FirebaseAuth.signOut().then((result) => {
                    console.log('Firebase sign out result:', result);

                    // Clear all local storage
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    } else {
                        // Fallback manual clearing
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('sessionData');
                        sessionStorage.clear();
                    }

                    // Clear app state
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }

                    window.dispatchEvent(new Event('authChanged'));

                    // Redirect to landing page for proper sign-in flow
                    console.log('🔄 Redirecting to landing page for sign-in...');
                    window.location.href = '/landing.html';
                }).catch(error => {
                    console.error('Error during sign out before sign in:', error);
                    // Force clear and redirect anyway
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    }
                    window.location.href = '/landing.html';
                });
            } else {
                // Firebase not available, clear manually and redirect
                if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                    window.CoreUtils.clearUserSession();
                } else {
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('sessionData');
                    sessionStorage.clear();
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }
                    window.dispatchEvent(new Event('authChanged'));
                }
                window.location.href = '/landing.html';
            }
        }

        function showSignUp() {
            alert('Sign Up functionality will be implemented with Firebase Authentication');
        }

        // Navigation functionality is now handled by navigation.js module
        // This comment replaces the duplicate navigation code that was removed

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add loading state management
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.3s ease-in-out';
        });

        // Add touch feedback for mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', function() {}, {passive: true});
        }
    </script>

    <!-- Create Post Modal -->
    <div id="postModal" class="post-modal" style="display: none;">
        <div class="post-modal-content">
            <div class="post-modal-header">
                <h3>Create New Post</h3>
                <button class="close-btn" onclick="Posts.closeCreatePostModal()">&times;</button>
            </div>
            <form id="postForm" class="post-form" data-validate data-action="/api/posts" data-reset-on-success="true">
                <div class="form-group">
                    <label for="postTitle">Title *</label>
                    <input
                        type="text"
                        id="postTitle"
                        name="title"
                        placeholder="What's your story about?"
                        required
                        data-validate="postTitle"
                        data-min-length="3"
                        data-max-length="100"
                        data-min-length-message="Title must be at least 3 characters"
                        data-max-length-message="Title must be no more than 100 characters">
                </div>
                <div class="form-group">
                    <label for="postContent">Content *</label>
                    <textarea
                        id="postContent"
                        name="content"
                        placeholder="Share your positive experience..."
                        rows="6"
                        required
                        data-validate="postContent"
                        data-min-length="10"
                        data-max-length="2000"
                        data-min-length-message="Content must be at least 10 characters"
                        data-max-length-message="Content must be no more than 2000 characters"></textarea>
                </div>
                <div id="postLoading" class="post-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>Creating your story...</span>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="Posts.closeCreatePostModal()">Cancel</button>
                    <button type="submit" class="submit-btn">Share Story</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Include external JavaScript files -->
    <script type="module" src="./public/js/performance-optimizer.js"></script>
    <script type="module" src="./public/js/mobile-optimizer.js"></script>
    <script type="module" src="./public/js/performance-dashboard.js"></script>
    <script type="module" src="./public/js/error-handler.js"></script>
    <script type="module" src="./public/js/form-validator.js"></script>
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>

    <!-- Additional functionality for navigation sections -->
    <script type="module">
        import { Authentication } from './public/js/authentication.js';
        import { FirebaseAuth } from './public/js/firebase-config.js';
        import { AppState } from './public/js/core.js';
        import { errorHandler } from './public/js/error-handler.js';
        import { formValidator } from './public/js/form-validator.js';

        // Initialize error handling and validation systems
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initializing Naroop application systems...');

            try {
                // Initialize performance optimization first for best results
                if (window.performanceOptimizer) {
                    await window.performanceOptimizer.init();
                    console.log('✅ Performance optimization initialized');
                }

                // Initialize mobile optimizer
                if (window.mobileOptimizer) {
                    await window.mobileOptimizer.init();
                    console.log('✅ Mobile optimizer initialized');
                }

                // Initialize performance dashboard
                if (window.performanceDashboard) {
                    await window.performanceDashboard.init();
                    console.log('✅ Performance dashboard initialized');
                }

                // Initialize error handling
                errorHandler.init();

                // Initialize form validation
                formValidator.init();

                // Add character counters to form fields
                const titleField = document.getElementById('postTitle');
                const contentField = document.getElementById('postContent');

                if (titleField) {
                    formValidator.addCharacterCounter(titleField, 100);
                }

                if (contentField) {
                    formValidator.addCharacterCounter(contentField, 2000);
                }

                console.log('✅ Error handling and validation systems initialized');

                // Apply performance optimizations to existing elements
                if (window.performanceOptimizer && window.performanceOptimizer.isReady()) {
                    // Add performance classes to key elements
                    document.querySelector('.header')?.classList.add('contain-all', 'gpu-accelerated');
                    document.querySelector('.sidebar')?.classList.add('contain-all', 'gpu-accelerated');
                    document.querySelector('.main-content')?.classList.add('contain-all');

                    // Optimize all buttons
                    document.querySelectorAll('button, .btn').forEach(btn => {
                        btn.classList.add('btn-optimized', 'touch-optimized');
                    });

                    // Optimize all form fields
                    document.querySelectorAll('input, textarea, select').forEach(field => {
                        field.classList.add('form-field');
                    });

                    console.log('✅ Performance optimizations applied to existing elements');
                }

                // Show initialization success notification
                setTimeout(() => {
                    errorHandler.showNotification(
                        'Naroop loaded successfully! 🚀',
                        'success',
                        3000
                    );
                }, 500);

            } catch (error) {
                console.error('❌ Error initializing systems:', error);
            }
        });

        // Initialize profile section when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load user profile data
            loadProfileData();

            // Setup sign out button
            const signOutBtn = document.getElementById('signOutBtn');
            if (signOutBtn) {
                signOutBtn.addEventListener('click', handleSignOut);
            }
        });

        // Load user profile data
        function loadProfileData() {
            try {
                const currentUser = localStorage.getItem('currentUser');
                if (currentUser) {
                    const userData = JSON.parse(currentUser);
                    
                    // Update profile UI
                    const profileUsername = document.getElementById('profileUsername');
                    const profileEmail = document.getElementById('profileEmail');
                    
                    if (profileUsername) {
                        profileUsername.textContent = userData.username || userData.displayName || 'User';
                    }
                    
                    if (profileEmail) {
                        profileEmail.textContent = userData.email || 'No email provided';
                    }
                }
            } catch (error) {
                console.error('Error loading profile data:', error);
            }
        }

        // Handle sign out
        async function handleSignOut() {
            try {
                const confirmSignOut = confirm('Are you sure you want to sign out?');
                if (!confirmSignOut) return;

                // Show loading state
                const signOutBtn = document.getElementById('signOutBtn');
                const originalText = signOutBtn.textContent;
                signOutBtn.textContent = 'Signing out...';
                signOutBtn.disabled = true;

                // Sign out using Firebase Auth
                const result = await FirebaseAuth.signOut();
                
                if (result.success) {
                    // Clear local storage
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    
                    // Redirect to landing page
                    window.location.href = '/landing.html';
                } else {
                    alert('Error signing out. Please try again.');
                    signOutBtn.textContent = originalText;
                    signOutBtn.disabled = false;
                }
            } catch (error) {
                console.error('Sign out error:', error);
                alert('Error signing out. Please try again.');
                
                // Reset button
                const signOutBtn = document.getElementById('signOutBtn');
                signOutBtn.textContent = 'Sign Out';
                signOutBtn.disabled = false;
            }
        }

        // Update profile data when navigation changes to profile section
        window.addEventListener('hashchange', function() {
            if (window.location.hash === '#profile') {
                loadProfileData();
            }
        });
    </script>

    <!-- Duplicate script loading removed - modules are loaded above -->
</body>
</html>
